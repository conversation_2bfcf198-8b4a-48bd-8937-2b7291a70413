{"compilerOptions": {"allowJs": false, "esModuleInterop": false, "isolatedModules": true, "jsx": "react-jsx", "jsxImportSource": "preact", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleDetection": "force", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "noUncheckedIndexedAccess": true, "paths": {"@/*": ["./src/*"]}, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022"}, "include": ["src"]}