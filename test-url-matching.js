// 测试URL匹配
const testUrl = 'https://x.com/i/api/graphql/YZH7qq3YaAGxUnWNTmpYlQ/HomeTimeline';
const homeTimelinePattern = /\/graphql\/.+\/HomeTimeline/;

console.log('测试URL:', testUrl);
console.log('正则表达式:', homeTimelinePattern);
console.log('匹配结果:', homeTimelinePattern.test(testUrl));

// 分析URL结构
console.log('\nURL结构分析:');
console.log('- 域名: x.com (而不是 twitter.com)');
console.log('- 路径: /i/api/graphql/YZH7qq3YaAGxUnWNTmpYlQ/HomeTimeline');
console.log('- GraphQL ID: YZH7qq3YaAGxUnWNTmpYlQ');
console.log('- 端点名称: HomeTimeline');

// 测试其他可能的URL格式
const testUrls = [
  'https://twitter.com/i/api/graphql/YZH7qq3YaAGxUnWNTmpYlQ/HomeTimeline',
  'https://x.com/i/api/graphql/YZH7qq3YaAGxUnWNTmpYlQ/HomeTimeline',
  '/i/api/graphql/YZH7qq3YaAGxUnWNTmpYlQ/HomeTimeline',
  'https://x.com/i/api/graphql/abc123/HomeTimeline'
];

console.log('\n测试多个URL格式:');
testUrls.forEach(url => {
  console.log(`${url} -> ${homeTimelinePattern.test(url)}`);
});
